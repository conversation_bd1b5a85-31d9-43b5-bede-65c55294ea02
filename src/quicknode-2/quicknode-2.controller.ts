import { Controller, Get, Post, Body, Patch, Param, Delete, HttpCode, HttpStatus } from '@nestjs/common';
import { Quicknode2Service } from './quicknode-2.service';
import { CreateQuicknode2Dto } from './dto/create-quicknode-2.dto';

@Controller('quicknode-2')
export class Quicknode2Controller {
  constructor(private readonly quicknode2Service: Quicknode2Service) {}

  // create webhook 
  @Post('create/webhook')
  async createWebhook(@Body() dto: CreateQuicknode2Dto) {
    console.log("Creating Quicknode 2 webhook");
    return await this.quicknode2Service.createWebhook(
      dto.name,
      dto.network, 
      dto.notification_email,
      dto.destination_url
    );
  }

  // create webhook with template ( filters, contract , wallet )

  // get all webhooks

  // get webhook by id 

  // update existing webhook 

  // delete webhook by id 

  // activate webhook by id 

  //pause webhook by id 

  //test filter 

}
