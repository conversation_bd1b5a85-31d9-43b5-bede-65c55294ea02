import { Transform } from "class-transformer";
import { IsEmail, IsEnum, IsString, IsUrl } from "class-validator";

export enum NetworkType {
  ETHEREUM = 'ethereum-mainnet',
  POLYGON = 'polygon-mainnet', 
  ARBITRUM = 'arbitrum-mainnet',
  BITCOIN = 'bitcoin-mainnet',
}

export class CreateQuicknode2Dto {
  @IsString()
  name: string;

  @IsEnum(NetworkType)
  network: NetworkType;

  @IsEmail()
  notification_email: string;

  @IsUrl()
  destination_url: string;

  get destination_attributes() {
    return {
      url: this.destination_url,
      compression: 'none'
    };
  }
}
