import { ApiProperty } from "@nestjs/swagger";
import { IsEmail, IsEnum, IsString, IsUrl } from "class-validator";

export enum NetworkType {
  ETHEREUM = 'ethereum-mainnet',
  POLYGON = 'polygon-mainnet', 
  ARBITRUM = 'arbitrum-mainnet',
  BITCOIN = 'bitcoin-mainnet',
}

export enum QuickNodeTemplate {
  EVM_WALLET_FILTER = 'evmWalletFilter',
  EVM_CONTRACT_EVENTS = 'evmContractEvents',
  SOLANA_WALLET_FILTER = 'solanaWalletFilter',
  BITCOIN_WALLET_FILTER = 'bitcoinWalletFilter',
  EVM_ABI_FILTER = 'evmAbiFilter',
}

export class CreateQnWebhookTemplateDto {
  @IsString()
  name: string;

  @IsEnum(NetworkType)
  network: NetworkType;

  @IsEmail()
  notification_email: string;

  @IsUrl()
  destination_url: string;

  get destination_attributes() {
    return {
      url: this.destination_url,
      compression: 'none'
    };
  }

  @IsEnum(QuickNodeTemplate)
  template: QuickNodeTemplate;

  @IsArray()
  @ArrayNotEmpty()
  templateArgs: {
    @IsEthereumAddress({ each: true })
    wallets: string[];
  };

}