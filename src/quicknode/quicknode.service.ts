import { HttpService } from '@nestjs/axios';
import { HttpException, HttpStatus, Injectable, Logger } from '@nestjs/common';
import { firstValueFrom, isObservable, Observable } from 'rxjs';
import { AxiosResponse } from 'axios';

@Injectable()
export class QuicknodeService {
  private readonly logger = new Logger(QuicknodeService.name);
  private readonly baseUrl = 'https://api.quicknode.com/webhooks/rest/v1';
  private readonly apiKey = process.env.QN_API_KEY as string;

  constructor(
    private readonly HttpService: HttpService) {
    if (!this.apiKey) {
      throw new Error('QN_API_KEY is not defined');
    }
  }

  private getHeaders() {
    return {
      'x-api-key': this.apiKey,
      'Content-Type': 'application/json',
    };
  }

  // lets make a service just for this ask kraken on opinion 
  protected async handleRequest<T>(
    request: Observable<AxiosResponse<T>> | Promise<AxiosResponse<T>>, // data kena error 
    operation: string
  ): Promise<T> {
    try {
      const response = isObservable(request)
        ? await firstValueFrom(request)
        : await request;

      this.logger.log(`YYYIPPPEEE ${operation} successful`);
      return response.data;
    } catch (error) {
      const status = error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR;
      const message = error.response?.data || `${operation} failed`;

      this.logger.error(`NEEIINN ${operation} failed:`, message);
      throw new HttpException(message, status);
    }
  }

  // create webhook 
  async createWebhook(name: string, network: string, notification_email: string, webhookUrl: string) {
    const webhookData =  {
      name,
      network,
      notification_email,
      destination_attributes: {
        url: webhookUrl,
        compression: 'none'
      },
      status: 'active',
    }

    const request = this.HttpService.post(
      `${this.baseUrl}/webhooks`,
      webhookData,
      { headers: this.getHeaders() }
    );
    return this.handleRequest(request, 'Create new webhook');
  }

  async createWebhookWithTemplate(name: string , network: string, noptification_email: string, webhookUrl: string, template: string, wallets: string[]) {
    const webhookData = {
      name,
      network,
      notification_email: noptification_email,
      destination_attributes: {
        url: webhookUrl,
        compression: 'none'
      },
      status: 'active',
      templateArgs: {
        wallets: wallets
      }
    };

    const request = this.HttpService.post(
      `${this.baseUrl}/webhooks/template/${template}`,
      webhookData,
      { headers: this.getHeaders() }
    );
    return this.handleRequest(request, 'Create new webhook with template');
  }

}
