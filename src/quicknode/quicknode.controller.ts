import { Controller, Get, Post, Body, Patch, Param, Delete } from '@nestjs/common';
import { QuicknodeService } from './quicknode.service';
import { CreateQnWebhookDto } from './dto/create-qn-webhook.dto';

@Controller('quicknode')
export class QuicknodeController {
  constructor(private readonly quicknodeService: QuicknodeService) {}

  // create webhook 
  @Post('create/webhook')
  async createWebhook(@Body() dto: CreateQnWebhookDto) {
    console.log("Creating Quicknode 2 webhook");
    return await this.quicknodeService.createWebhook(
      dto.name,
      dto.network, 
      dto.notification_email,
      dto.destination_url
    );
  }

  // create webhook with template ( filters, contract , wallet )

  // get all webhooks

  // get webhook by id 

  // update existing webhook 

  // delete webhook by id 

  // activate webhook by id 

  //pause webhook by id 

  //test filter 

}
