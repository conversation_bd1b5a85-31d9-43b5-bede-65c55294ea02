import { Controller, Get, Post, Body, Patch, Param, Delete, Query } from '@nestjs/common';
import { QuicknodeService } from './quicknode.service';
import { CreateQnWebhookDto } from './dto/create-qn-webhook.dto';
import { CreateQnWebhookTemplateDto } from './dto/create-qn-webhook-template.dto';
import { GetAllQnWebhookPaginationDto } from './dto/getAll-qn-webhook-pagination.dto';

@Controller('quicknode')
export class QuicknodeController {
  constructor(private readonly quicknodeService: QuicknodeService) {}

  // create webhook 
  @Post('create/webhook')
  async createWebhook(@Body() dto: CreateQnWebhookDto) {
    console.log("Creating Quicknode webhook");
    return await this.quicknodeService.createWebhook(
      dto.name,
      dto.network, 
      dto.notification_email,
      dto.destination_url
    );
  }

  // create webhook with template ( filters, contract , wallet )
  @Post('create/webhook/template/evmWalletFilter')
  async createWebhookWithTemplate(@Body() dto: CreateQnWebhookTemplateDto) {
    console.log("Creating Quicknode webhook with template");
    const wallets = dto.templateArgs?.wallets || [];
    console.log("These are the existing wallets to filter\n", wallets);
    return await this.quicknodeService.createWebhookWithTemplate(
      dto.name,
      dto.network,
      dto.notification_email,
      dto.destination_url,
      dto.template,
      wallets
    );
  }

  // get all webhooks
  @Get("retrieve/webhooks")
  async getAllWebhooks(@Query() dto: GetAllQnWebhookPaginationDto) {
    console.log('🔍 DEBUG - Raw dto:', dto);
    console.log('🔍 DEBUG - dto.page:', dto.page, typeof dto.page);
    console.log('🔍 DEBUG - dto.limit:', dto.limit, typeof dto.limit);
    return await this.quicknodeService.getAllWebhooks(
      dto.limit,
      dto.offset
    );
  }

  // get webhook by id 

  // update existing webhook 

  // delete webhook by id 

  // activate webhook by id 

  //pause webhook by id 

  //test filter 

}
