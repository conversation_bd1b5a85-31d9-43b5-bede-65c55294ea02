import { <PERSON>, Get, Post, Body, Patch, Param, Delete } from '@nestjs/common';
import { QuicknodeService } from './quicknode.service';
import { CreateQnWebhookDto } from './dto/create-qn-webhook.dto';
import { CreateQnWebhookTemplateDto } from './dto/create-qn-webhook-template.dto';

@Controller('quicknode')
export class QuicknodeController {
  constructor(private readonly quicknodeService: QuicknodeService) {}

  // create webhook 
  @Post('create/webhook')
  async createWebhook(@Body() dto: CreateQnWebhookDto) {
    console.log("Creating Quicknode 2 webhook");
    return await this.quicknodeService.createWebhook(
      dto.name,
      dto.network, 
      dto.notification_email,
      dto.destination_url
    );
  }

  @Post('create/webhook/template')
  async createWebhookWithTemplate(@Body() dto: CreateQnWebhookTemplateDto) {
    console.log("Creating Quicknode 2 webhook with template");
    console.log(dto.templateArgs.wallets);
    return await this.quicknodeService.createWebhookWithTemplate(
      dto.name,
      dto.network, 
      dto.notification_email,
      dto.destination_url,
      dto.template,
      dto.templateArgs.wallets
    );
  }

  // create webhook with template ( filters, contract , wallet )

  // get all webhooks

  // get webhook by id 

  // update existing webhook 

  // delete webhook by id 

  // activate webhook by id 

  //pause webhook by id 

  //test filter 

}
